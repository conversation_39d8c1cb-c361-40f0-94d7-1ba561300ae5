#include "gray_app.h"

float gray_weights[8]={-4.0f,-3.0f,-2.0f,-1.0f,1.0f,2.0f,3.0f,4.0f};

float gray_line_position_error; // 循迹误差值（反馈量）

//感为
No_MCU_Sensor sensor;
unsigned short white[8]={3319,3366,3308,3315,3264,3308,3282,3343};
unsigned short black[8]={1577,2355,2190,2361,1629,1108,1390,2055};
unsigned char Digtal;

unsigned char rx_buff[256]={0};
unsigned short Anolog[8]={0};

void Gray_Init(void)
{
	sprintf((char *)rx_buff,"hello_world!\r\n");
	uart1_send_string((char *)rx_buff);
	memset(rx_buff,0,256);
	//初始化传感器，不带黑白忿
	No_MCU_Ganv_Sensor_Init_Frist(&sensor);
	No_Mcu_Ganv_Sensor_Task_Without_tick(&sensor);
	Get_Anolog_Value(&sensor,Anolog);
	//此时打印的ADC的忼，可用通过这个ADC作为黑白值的校准
	//也可以自己写按键逻辑完成丿键校准功胿
	sprintf((char *)rx_buff,"Anolog %d-%d-%d-%d-%d-%d-%d-%d\r\n",Anolog[0],Anolog[1],Anolog[2],Anolog[3],Anolog[4],Anolog[5],Anolog[6],Anolog[7]);
	uart1_send_string((char *)rx_buff);
	HAL_Delay(100);
	memset(rx_buff,0,256);
	//得到黑白校准值之后，初始化传感器
	No_MCU_Ganv_Sensor_Init(&sensor,white,black);

	HAL_Delay(100);
}

void Gray_Task(void)
{
			//无时基传感器常规任务，包含模拟量，数字量，归丿化量
		No_Mcu_Ganv_Sensor_Task_Without_tick(&sensor);
			//获取传感器数字量结果(只有当有黑白值传入进去了之后才会有这个忼！＿)
		Digtal=~(Get_Digtal_For_User(&sensor));
//		Uart_Printf(&huart5,"Digtal %d-%d-%d-%d-%d-%d-%d-%d\r\n",(Digtal>>0)&0x01,(Digtal>>1)&0x01,(Digtal>>2)&0x01,(Digtal>>3)&0x01,(Digtal>>4)&0x01,(Digtal>>5)&0x01,(Digtal>>6)&0x01,(Digtal>>7)&0x01);
//		Uart_Printf(&huart5,"yaw %.2f\r\n",yaw);

		float weights_sum=0;
		unsigned char black_line_count=0;
	
		for(uint8_t i=0;i<8;i++)
		{
			if((Digtal>>i)&0x01)
			{
				weights_sum += gray_weights[i];
				black_line_count++;
			}
		}
		
		if(black_line_count>0)
		{
			gray_line_position_error=weights_sum/black_line_count;
		}
}



