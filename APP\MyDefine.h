#ifndef __MYDEFINE_H__
#define __MYDEFINE_H__

/* ========== HAL 库头文件 ========== */
#include "main.h"
#include "gpio.h"
#include "dma.h"
#include "tim.h"
#include "usart.h"
#include "i2c.h"
#include "adc.h"

/* ========== C 语言头文件 ========== */
#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <math.h>
#include <stdint.h>
#include <stdbool.h>

/* ========== 核心调度器头文件 ========== */
#include "Scheduler.h"

/* ========== 组件库头文件 ========== */
#include "ebtn.h"
#include "hardware_iic.h"
#include "pid.h"
#include "ringbuffer.h"

#include "No_Mcu_Ganv_Grayscale_Sensor_Config.h"
#include "oled.h"

#include "bno08x_hal.h"

/* ========== 驱动库头文件 ========== */
#include "motor_driver.h"
#include "encoder_driver.h"
#include "ebtn_driver.h"
#include "led_driver.h"
#include "uart_driver.h"

/* ========== 应用层头文件 ========== */
#include "motor_app.h"
#include "encoder_app.h"
#include "key_app.h"
#include "led_app.h"
#include "gray_app.h"
#include "pid_app.h"
#include "uart_app.h"
#include "oled_app.h"
#include "bno08x_app.h"

/* ========== 全局用户变量 ========== */
extern Motor_t right_motor;
extern Motor_t left_motor; 

extern uint8_t led_buf[4];

extern unsigned char gray_ff_count; 

#endif


