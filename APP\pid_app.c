#include "pid_app.h"

int basic_speed = 40;//基础速度
/* PID 控制器实例 */
PID_T pid_speed_left;  // 左轮速度环
PID_T pid_speed_right; // 右轮速度环

PID_T pid_line;//循迹环

PID_T pid_angle;			 //角度环

unsigned char pid_mode = 1;//  1-角度环控制   0-循迹环控制

// 增量式PID：P-稳定性，I-响应性，D-准确性

//转速49误差0--0.2
PidParams_t pid_params_left = {
    .kp = 2.5f,      
    .ki = 0.13f,  
    .kd = 20.00f,  
	.out_min = -100.0f,
    .out_max = 100.0f,
};


PidParams_t pid_params_right = {
		.kp = 0.93f,
    .ki = 0.05f,
    .kd = 20.86f,
    .out_min = -100.0f,
    .out_max = 101.90f,
};

PidParams_t pid_params_line = {
		.kp = 4.7f,
//		.kp = 0.8f,
//		.kp = 2.3f,
    .ki = 0.0f,
    .kd = 0.0f,
    .out_min = -100.0f,
    .out_max = 100.0f,
};

PidParams_t pid_params_angle = {
    .kp = 2.4f,      // 略降Kp
    .ki = 0.0f,   // 保持Ki
    .kd = 0.0f,      // 增加Kd抑制振荡
    .out_min = -100.0f,
    .out_max = 100.0f,
};

void PID_Init(void)
{
  pid_init(&pid_speed_left,
           pid_params_left.kp, pid_params_left.ki, pid_params_left.kd,
           0.0f, pid_params_left.out_max);
  
  pid_init(&pid_speed_right,
           pid_params_right.kp, pid_params_right.ki, pid_params_right.kd,
           0.0f, pid_params_right.out_max);
  pid_init(&pid_line,
           pid_params_line.kp, pid_params_line.ki, pid_params_line.kd,
           0.0f, pid_params_line.out_max);
  
  pid_init(&pid_angle,
           pid_params_angle.kp, pid_params_angle.ki, pid_params_angle.kd,
           0.0f, pid_params_angle.out_max);
	
  pid_set_target(&pid_speed_left, basic_speed);
  pid_set_target(&pid_speed_right, basic_speed);
  pid_set_target(&pid_line, 0);	
	pid_set_target(&pid_angle, 0);
}

// 低通滤波器系数 (Low-pass filter coefficient 'alpha')
// alpha 越小, 滤波效果越强, 但延迟越大。建议从 0.1 到 0.5 之间开始尝试。
#define SPEED_FILTER_ALPHA_LEFT  0.146f 
#define SPEED_FILTER_ALPHA_RIGHT 0.15f 

// 用于存储滤波后速度的变量
static float filtered_speed_left = 0.0f;
static float filtered_speed_right = 0.0f;


bool pid_running = true; // PID 控制使能开关

void pid_line_control(void)
{
		int pid_line_output = 0;
	
		pid_line_output = pid_calculate_positional(&pid_line,gray_line_position_error);

		pid_line_output = pid_constrain(pid_line_output, pid_params_line.out_min, pid_params_line.out_max);
  
		pid_set_target(&pid_speed_left,basic_speed - pid_line_output);
	
		pid_set_target(&pid_speed_right, basic_speed + pid_line_output);
}


void PID_Task(void)
{
    if(pid_running == false) return;

    float output_left, output_right;
		
		if(pid_mode == 0)
		{
			pid_line_control();
		}
	
    // filtered = alpha * raw + (1 - alpha) * previous_filtered
    filtered_speed_left = SPEED_FILTER_ALPHA_LEFT * left_encoder.speed_cm_s + (1.0f - SPEED_FILTER_ALPHA_LEFT) * filtered_speed_left;
    filtered_speed_right = SPEED_FILTER_ALPHA_RIGHT * right_encoder.speed_cm_s + (1.0f - SPEED_FILTER_ALPHA_RIGHT) * filtered_speed_right;

    output_left = pid_calculate_positional(&pid_speed_left, filtered_speed_left);
    output_right = pid_calculate_positional(&pid_speed_right, filtered_speed_right);
				
		if(pid_mode == 1) //为真时角度环控制
		{ 
			int  angle_output = 0;
			
      angle_output = pid_calculate_positional(&pid_angle, yaw);
			
			angle_output = pid_constrain(angle_output,pid_params_angle.out_min,pid_params_angle.out_max);
			
      output_left = output_left - angle_output;
      output_right = output_right + angle_output;
		}	

		//output_yaw =  pid_calculate_incremental(&pid_yaw, yaw); -> PID计算 YAW -> 反馈
		//pid_set_target(&pid_speed_left,v + output_yaw); -> 作用对象
		//pid_set_target(&pid_speed_right,v - output_yaw);
    output_left = pid_constrain(output_left, pid_params_left.out_min, pid_params_left.out_max);
    output_right = pid_constrain(output_right, pid_params_right.out_min, pid_params_right.out_max);
    // 设置电机速度
    Motor_SetSpeed(&left_motor, output_left+0.91);
    Motor_SetSpeed(&right_motor, output_right);
//		my_printf(&huart1,"{output_left}%.2f\r\n", output_left);
//		my_printf(&huart1,"{output_right}%.2f\r\n", output_right);
    my_printf(&huart1,"{left_filtered}%.2f,%.2f\r\n", pid_speed_left.target, filtered_speed_left);
		my_printf(&huart1,"{right_filtered}%.2f,%.2f\r\n", pid_speed_right.target, filtered_speed_right);
//    my_printf(&huart1,"{left_right}%.2f,%.2f\r\n", filtered_speed_left,filtered_speed_right);
}


