#include "bno08x_app.h"
#include "bno08x_hal.h"

extern UART_HandleTypeDef huart1;
extern I2C_HandleTypeDef hi2c1;

float roll, pitch, yaw;
float convert_to_continuous_yaw(float current_yaw);

int8_t my_bno080_init(void)
{
	 BNO080_Init(&hi2c1, BNO080_DEFAULT_ADDRESS); // I2C初始化

	 if (BNO080_HardwareReset() != 0) { // 硬件复位失败则用软件复位
			 softReset();
			 HAL_Delay(100);
	 }

	 enableRotationVector(100); // 启用姿态数据，100ms更新
	 HAL_Delay(200);
	 //初始化完成
	 return 0;
}

uint8_t first_flat = 0;
float frist_yaw = 0;

/**
 * @brief BNO080传感器完整初始化和配置函数
 * @note 包含硬件初始化、复位、传感器配置等完整流程
 * @note 用户可通过注释选择性启用不同功能模块
 * @retval 0: 成功, -1: 失败
 */
void bno080_task(void)
{

    // 检查是否有新数据
    if (dataAvailable()) 
		{
        //四元数和姿态数据（基础功能）
        float quat_i = getQuatI();
        float quat_j = getQuatJ();
        float quat_k = getQuatK();
        float quat_real = getQuatReal();
        QuaternionToEulerAngles(quat_i, quat_j, quat_k, quat_real, &roll, &pitch, &yaw);
				if(first_flat == 0)
				{
					first_flat = 1;
					frist_yaw = yaw;
				}
				yaw = yaw-frist_yaw;
//				Uart_Printf(&huart1, "Euler: %.2f, %.2f, %.2f\r\n", roll, pitch, yaw);
		}
}

float get_roll(void)
{
	return roll;
}

float get_pitch(void)
{
	return pitch;
}

float get_yaw(void)
{
	float YAW = convert_to_continuous_yaw(yaw);
	return YAW;
}

// ʹ�þ�̬������������һ�ε�״̬
float g_last_yaw = 0.0f;
int g_revolution_count = 0;
bool g_is_yaw_initialized = false;
/**
 * @brief ��һ����[-180, 180]��Χ�ڵ�yaw�Ƕ�ת��Ϊ�����ĽǶ�ֵ��
 * 
 * @param current_yaw �Ӵ�������ȡ�ĵ�ǰyawֵ (-180 to 180)��
 * @return float ������yaw�Ƕ�ֵ (���� 370, -450 ��)��
 */
float convert_to_continuous_yaw(float current_yaw) 
{
    // ����һ����ֵ����⡰���䡱�����ֵӦ�ô���180��ͨ��ȡ270��300�Ƚϰ�ȫ��
    const float WRAP_AROUND_THRESHOLD = 300.0f;

    // �״ε���ʱ���г�ʼ��
    if (!g_is_yaw_initialized) {
        g_last_yaw = current_yaw;
        g_is_yaw_initialized = true;
        g_revolution_count = 0;
    }

    // �������ϴζ����Ĳ���
    float diff = current_yaw - g_last_yaw;

    // ����Ƿ����ˡ����䡱
    if (diff > WRAP_AROUND_THRESHOLD) {
        // �����Ƕ��������Ƕ� (����, �� 170�� �� -175��), ʵ��������ת, Ȧ��Ӧ������
        // ��ʱ diff �ӽ� -360 (���� -175 - 170 = -345)
        // ����߼��������Ǵ�-180���䵽+180�������˵��������ת����
        g_revolution_count--;
    } else if (diff < -WRAP_AROUND_THRESHOLD) {
        // �Ӹ��Ƕ��������Ƕ� (����, �� -170�� �� 175��), ʵ��������ת, Ȧ��Ӧ�ü�С
        // ��ʱ diff �ӽ� 360 (���� 175 - (-170) = 345)
        // ����߼��������Ǵ�+180���䵽-180�������˵��������ת����
        g_revolution_count++;
    }

    // �����ϴε�yawֵ�Ա��´ε���
    g_last_yaw = current_yaw;

    // ����������yawֵ
    float continuous_yaw = current_yaw + (float)g_revolution_count * 360.0f;

    return continuous_yaw;
}
