#include "scheduler.h"
#include "pid_app.h"

// 任务结构体
typedef struct {
  void (*task_func)(void);  // 任务函数指针
  uint32_t rate_ms;         // 执行周期（毫秒）
  uint32_t last_run;        // 上次执行时间（初始化为 0，每次运行时刷新）
} scheduler_task_t;

uint8_t task_num;// 全局变量，用于存储任务数量

unsigned char gray_ff_count = 0;    //出圈 入圈计数
unsigned char measure_time5ms;

unsigned char gray_into_ff_flag;
unsigned int out_timer1000ms;

unsigned char gray_out_ff_flag;
unsigned int into_timer1000ms;
unsigned int  out_timer=1000;
unsigned int int_timer=800;
/**
 * @brief 用户初始化函数
 * 非HAL库硬件初始化函数
 */
void System_Init()
{
////	//初始化陀螺仪
//    my_bno080_init();
//	// 初始化电机
	Motor_Init();
//	// 初始化编码器
	Encoder_Init();
//// 
//  Led_Init();
//  Key_Init();
//  Uart_Init();
//  Gray_Init();
//  OLED_Init();
//  PID_Init();
  HAL_TIM_Base_Start_IT(&htim2);
}

void Car_State_Update(void)
{
	if(gray_ff_count == 1)
	{	
		pid_mode = 0;  // 切换到循迹模式	
		pid_set_target(&pid_speed_left, 40);
		pid_set_target(&pid_speed_right, 40);
		
	}
	else if(gray_ff_count == 2)
	{
    pid_set_target(&pid_angle,150.0);
		pid_mode = 1;  
	}
	else if(gray_ff_count == 3)
	{		
		pid_set_target(&pid_speed_left, 0);
		pid_set_target(&pid_speed_left, 40);
		pid_set_target(&pid_speed_right, 40);
		pid_mode = 0;  // 切换到循迹模式
	}
	else if(gray_ff_count == 4)
	{
		Motor_brake(&left_motor);
		Motor_brake(&right_motor);
		gray_ff_count=0;
		pid_running = 0;     // 停止PID运行
	}


}

//定时器2 中断服务函数（1ms 中断）
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef  *htim)
{
	if(htim->Instance != htim2.Instance) return;
	
	if(++measure_time5ms >= 5) //每5ms执行检测任务
	{
		measure_time5ms = 0;
		Encoder_Task();
//		bno080_task();
//		Gray_Task();
//		PID_Task();
	}

//  /* 入圈触发器 */
//  if(Digtal != 0x00)
//  {
//    gray_into_ff_flag = 1;
//    if(++out_timer1000ms >= out_timer) out_timer1000ms = out_timer;
//  }
//  else if((gray_into_ff_flag==1)&&(out_timer1000ms==out_timer))
//  {
//    gray_into_ff_flag = 0;
//      gray_ff_count++;
//      Car_State_Update();
//    out_timer1000ms = 0;
//  }
////	else
////	{
////    gray_into_ff_flag = 0;
////		out_timer1000ms = 0;
////	}
//  
//  /* 出圈触发器 */
//  if(Digtal == 0x00)
//  {
//    gray_out_ff_flag = 1;
//    if(++into_timer1000ms >= int_timer) into_timer1000ms = int_timer;
//  }
//  else if((gray_out_ff_flag==1)&&(into_timer1000ms==int_timer))
//  {
//    gray_out_ff_flag = 0;
//      gray_ff_count++;
//      Car_State_Update();
//    into_timer1000ms = 0;
//  }
////	else
////	{
////    gray_out_ff_flag = 0;
////		into_timer1000ms = 0;
////	}
}

// 静态任务数组，每个任务包含任务函数、执行周期（毫秒）和上次运行时间（毫秒）
static scheduler_task_t scheduler_task[] =
{
//  {Uart_Task, 10, 0},
//	{Led_Task, 1, 0},
//	{Key_Task,1, 0},
//  {oled_task, 1, 0},
	{Uart5_Task, 10, 0},
};


/**
 * @brief 调度器初始化函数
 * 计算任务数组的元素个数，并将结果存储在 task_num 中
 */
void Scheduler_Init(void)
{
  System_Init();
  // 计算任务数组的元素个数，并将结果存储在 task_num 中
  task_num = sizeof(scheduler_task) / sizeof(scheduler_task_t); // 数组大小 / 数组成员大小 = 数组元素个数
}

/**
 * @brief 调度器运行函数
 * 遍历任务数组，检查是否有任务需要执行。如果当前时间已经超过任务的执行周期，则执行该任务并更新上次运行时间
 */
void Scheduler_Run(void)
{
  // 遍历任务数组中的所有任务
  for (uint8_t i = 0; i < task_num; i++)
  {
    // 获取当前的系统时间（毫秒）
    uint32_t now_time = HAL_GetTick();

    // 检查当前时间是否达到任务的执行时间
    if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
    {
      // 更新任务的上次运行时间为当前时间
      scheduler_task[i].last_run = now_time;

      // 执行任务函数
      scheduler_task[i].task_func();
    }
  }
}




