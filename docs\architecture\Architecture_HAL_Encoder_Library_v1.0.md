# HAL硬件编码器库技术架构设计文档

## 1. 文档信息

| 项目 | 信息 |
|------|------|
| 文档标题 | HAL硬件编码器库技术架构设计 |
| 版本号 | v1.0 |
| 创建日期 | 2025-01-04 |
| 负责人 | Bob (架构师) |
| 项目名称 | Car_Xifeng_F4 HAL编码器库 |
| 目标平台 | STM32F407 + HAL库 |

## 2. 架构概述

### 2.1 设计原则
基于现有Motor和HWT101组件的成功架构模式，编码器库采用以下核心设计原则：

1. **三层数据结构架构**：硬件配置层 + 数据层 + 主实体层
2. **硬件抽象封装**：通过HAL库句柄实现硬件无关性
3. **状态驱动设计**：完整的状态机管理和错误处理
4. **滤波增强稳定性**：集成多种滤波算法确保数据稳定性
5. **低耦合高内聚**：最小化依赖，最大化功能内聚

### 2.2 核心技术特性

#### 2.2.1 硬件定时器编码器模式
- **STM32硬件支持**：利用STM32F407的TIM编码器模式
- **正交解码**：硬件自动处理A/B相正交信号
- **方向检测**：硬件自动判断旋转方向
- **计数精度**：32位硬件计数器，无软件轮询开销

#### 2.2.2 多级滤波系统（新增核心特性）
- **硬件滤波**：定时器输入捕获滤波器
- **软件滤波**：移动平均滤波、卡尔曼滤波、中值滤波
- **自适应滤波**：根据速度动态调整滤波参数
- **异常值检测**：检测和剔除异常数据点

## 3. 系统架构设计

### 3.1 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (APP Layer)                        │
├─────────────────────────────────────────────────────────────┤
│  encoder_app.h/c  │  简化接口  │  全局实例  │  初始化封装    │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   组件层 (Component Layer)                   │
├─────────────────────────────────────────────────────────────┤
│                    encoder_driver.h/c                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   数据获取   │  │   滤波处理   │  │   状态管理   │         │
│  │   模块      │  │   模块      │  │   模块      │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   硬件配置   │  │   参数验证   │  │   错误处理   │         │
│  │   模块      │  │   模块      │  │   模块      │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   硬件抽象层 (HAL Layer)                     │
├─────────────────────────────────────────────────────────────┤
│  STM32 HAL TIM  │  STM32 HAL GPIO  │  STM32 HAL NVIC      │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    硬件层 (Hardware Layer)                   │
├─────────────────────────────────────────────────────────────┤
│  TIM编码器模式   │  GPIO输入      │  编码器硬件             │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 数据结构架构

#### 3.2.1 核心数据结构设计

```c
/**
 * @brief 编码器滤波配置结构体（新增）
 */
typedef struct {
    uint8_t enable_filter;          // 滤波使能标志
    uint8_t filter_type;            // 滤波类型（移动平均/卡尔曼/中值）
    uint8_t window_size;            // 滤波窗口大小
    float noise_variance;           // 噪声方差（卡尔曼滤波用）
    float process_variance;         // 过程方差（卡尔曼滤波用）
    uint16_t outlier_threshold;     // 异常值阈值
} EncoderFilter_t;

/**
 * @brief 编码器硬件配置结构体（增强版）
 */
typedef struct {
    TIM_HandleTypeDef* htim;        // 定时器句柄
    uint32_t tim_channel_a;         // A相输入通道
    uint32_t tim_channel_b;         // B相输入通道
    GPIO_TypeDef* z_port;           // Z相GPIO端口（可选）
    uint16_t z_pin;                 // Z相GPIO引脚（可选）
    uint32_t ppr;                   // 每转脉冲数
    uint8_t reverse;                // 方向反转标志
    uint32_t max_rpm;               // 最大转速限制
    uint8_t input_filter;           // 硬件输入滤波器配置
    EncoderFilter_t filter_config;  // 软件滤波配置
} EncoderHW_t;

/**
 * @brief 编码器滤波数据结构体（新增）
 */
typedef struct {
    float position_buffer[16];      // 位置滤波缓冲区
    float velocity_buffer[8];       // 速度滤波缓冲区
    uint8_t position_index;         // 位置缓冲区索引
    uint8_t velocity_index;         // 速度缓冲区索引
    float kalman_position_est;      // 卡尔曼位置估计
    float kalman_velocity_est;      // 卡尔曼速度估计
    float kalman_error_cov[2][2];   // 卡尔曼误差协方差矩阵
    uint32_t outlier_count;         // 异常值计数
} EncoderFilterData_t;

/**
 * @brief 编码器数据结构体（增强版）
 */
typedef struct {
    // 原始数据
    int32_t position_raw;           // 原始位置计数
    int32_t position_mm_raw;        // 原始位置（毫米）
    float velocity_rpm_raw;         // 原始速度（转/分钟）
    float velocity_mm_s_raw;        // 原始线速度（毫米/秒）
    
    // 滤波后数据
    int32_t position_mm;            // 滤波后位置（毫米）
    float velocity_rpm;             // 滤波后速度（转/分钟）
    float velocity_mm_s;            // 滤波后线速度（毫米/秒）
    
    // 状态数据
    int8_t direction;               // 方向（1正转，-1反转，0停止）
    uint32_t revolution_count;      // 圈数计数
    uint32_t timestamp;             // 数据时间戳
    uint8_t data_valid;             // 数据有效标志
    uint8_t z_pulse_detected;       // Z脉冲检测标志
    
    // 滤波相关
    EncoderFilterData_t filter_data; // 滤波数据
    uint8_t filter_ready;           // 滤波器就绪标志
} EncoderData_t;

/**
 * @brief 编码器主实体结构体（最终版）
 */
typedef struct {
    EncoderHW_t hw;                 // 硬件配置
    EncoderData_t data;             // 编码器数据
    EncoderType_t type;             // 编码器类型
    EncoderState_t state;           // 当前状态
    uint8_t enable;                 // 使能标志
    float mm_per_pulse;             // 毫米每脉冲转换系数
    int32_t last_position;          // 上次位置（用于速度计算）
    uint32_t last_timestamp;        // 上次时间戳
    uint32_t update_interval_ms;    // 数据更新间隔
} Encoder_t;
```

### 3.3 滤波算法架构

#### 3.3.1 多级滤波策略

```c
/**
 * @brief 滤波类型枚举
 */
typedef enum {
    ENCODER_FILTER_NONE = 0,        // 无滤波
    ENCODER_FILTER_MOVING_AVG,      // 移动平均滤波
    ENCODER_FILTER_KALMAN,          // 卡尔曼滤波
    ENCODER_FILTER_MEDIAN,          // 中值滤波
    ENCODER_FILTER_ADAPTIVE         // 自适应滤波
} EncoderFilterType_t;

/**
 * @brief 滤波处理流程
 */
// 1. 硬件滤波（定时器输入捕获滤波器）
// 2. 异常值检测和剔除
// 3. 主滤波算法（移动平均/卡尔曼/中值）
// 4. 自适应参数调整
// 5. 输出稳定的数据
```

## 4. 核心模块设计

### 4.1 硬件配置模块

#### 4.1.1 定时器编码器模式配置
```c
/**
 * @brief 配置定时器编码器模式
 * @param encoder: 编码器实体指针
 * @retval 0: 成功, -1: 失败
 */
static int8_t Encoder_ConfigTimerEncoderMode(Encoder_t* encoder)
{
    // 1. 配置定时器为编码器模式
    // 2. 设置输入滤波器
    // 3. 配置计数方向和边沿
    // 4. 启动定时器
}
```

#### 4.1.2 硬件滤波配置
```c
/**
 * @brief 配置硬件输入滤波器
 * @param encoder: 编码器实体指针
 * @retval 0: 成功, -1: 失败
 */
static int8_t Encoder_ConfigHardwareFilter(Encoder_t* encoder)
{
    // 配置TIM输入捕获滤波器
    // 滤波器时钟频率 = TIM时钟 / (2^N)
    // N = 0~15，可配置不同的滤波强度
}
```

### 4.2 数据获取模块

#### 4.2.1 位置数据获取
```c
/**
 * @brief 获取原始位置数据
 * @param encoder: 编码器实体指针
 * @retval 位置计数值
 */
static int32_t Encoder_GetRawPosition(Encoder_t* encoder)
{
    // 读取定时器计数器值
    // 处理计数器溢出
    // 计算绝对位置
}
```

#### 4.2.2 速度计算
```c
/**
 * @brief 计算速度
 * @param encoder: 编码器实体指针
 * @retval 0: 成功, -1: 失败
 */
static int8_t Encoder_CalculateVelocity(Encoder_t* encoder)
{
    // 基于位置差和时间差计算速度
    // 处理方向变化
    // 计算RPM和线速度
}
```

### 4.3 滤波处理模块（核心新增）

#### 4.3.1 移动平均滤波
```c
/**
 * @brief 移动平均滤波
 * @param encoder: 编码器实体指针
 * @param raw_value: 原始值
 * @param buffer: 滤波缓冲区
 * @param index: 缓冲区索引指针
 * @param window_size: 窗口大小
 * @retval 滤波后的值
 */
static float Encoder_MovingAverageFilter(Encoder_t* encoder, 
                                       float raw_value,
                                       float* buffer,
                                       uint8_t* index,
                                       uint8_t window_size)
{
    // 1. 将新值存入循环缓冲区
    // 2. 计算窗口内所有值的平均值
    // 3. 返回滤波结果
}
```

#### 4.3.2 卡尔曼滤波
```c
/**
 * @brief 卡尔曼滤波（用于位置和速度估计）
 * @param encoder: 编码器实体指针
 * @param measurement: 测量值
 * @retval 滤波后的估计值
 */
static float Encoder_KalmanFilter(Encoder_t* encoder, float measurement)
{
    // 1. 预测步骤：预测状态和误差协方差
    // 2. 更新步骤：计算卡尔曼增益，更新状态估计
    // 3. 返回最优估计值
}
```

#### 4.3.3 异常值检测
```c
/**
 * @brief 异常值检测和处理
 * @param encoder: 编码器实体指针
 * @param value: 待检测的值
 * @retval 1: 正常值, 0: 异常值
 */
static uint8_t Encoder_OutlierDetection(Encoder_t* encoder, float value)
{
    // 1. 基于统计方法检测异常值
    // 2. 3-sigma规则或IQR方法
    // 3. 记录异常值统计信息
}
```

### 4.4 状态管理模块

#### 4.4.1 状态机设计
```c
/**
 * @brief 编码器状态枚举（完整版）
 */
typedef enum {
    ENCODER_STATE_IDLE = 0,         // 空闲状态
    ENCODER_STATE_INITIALIZING,     // 初始化状态
    ENCODER_STATE_RUNNING,          // 运行状态
    ENCODER_STATE_CALIBRATING,      // 校准状态
    ENCODER_STATE_ERROR,            // 错误状态
    ENCODER_STATE_FILTER_LEARNING   // 滤波器学习状态
} EncoderState_t;
```

#### 4.4.2 错误处理机制
```c
/**
 * @brief 错误类型枚举
 */
typedef enum {
    ENCODER_ERROR_NONE = 0,         // 无错误
    ENCODER_ERROR_HARDWARE,         // 硬件错误
    ENCODER_ERROR_SIGNAL_LOST,      // 信号丢失
    ENCODER_ERROR_SPEED_OVERFLOW,   // 速度溢出
    ENCODER_ERROR_FILTER_FAIL       // 滤波器失效
} EncoderError_t;
```

## 5. 性能优化设计

### 5.1 内存优化
- **静态内存分配**：避免动态内存分配
- **缓冲区大小优化**：根据实际需求调整滤波缓冲区大小
- **数据结构对齐**：确保结构体内存对齐，提高访问效率

### 5.2 计算优化
- **整数运算优先**：尽量使用整数运算，减少浮点计算
- **查表法**：三角函数等复杂运算使用查表法
- **位运算优化**：使用位运算替代除法和模运算

### 5.3 实时性优化
- **硬件定时器**：使用硬件定时器减少CPU占用
- **中断优先级**：合理设置中断优先级
- **数据更新策略**：采用定时更新而非实时计算

## 6. 接口设计规范

### 6.1 函数命名规范
遵循现有组件库的命名规范：
- **创建函数**：`Encoder_Create()`
- **使能函数**：`Encoder_Enable()` / `Encoder_Disable()`
- **数据获取**：`Encoder_GetPosition()` / `Encoder_GetVelocity()`
- **配置函数**：`Encoder_SetConversionFactor()` / `Encoder_SetFilterConfig()`
- **状态管理**：`Encoder_GetState()` / `Encoder_Update()`

### 6.2 错误处理规范
- **返回值约定**：成功返回0，失败返回-1
- **参数验证**：所有公共函数进行参数有效性检查
- **状态检查**：检查使能状态和数据有效性
- **安全默认值**：错误时返回安全的默认值

### 6.3 滤波接口设计（新增）
```c
// 滤波配置接口
int8_t Encoder_SetFilterType(Encoder_t* encoder, EncoderFilterType_t type);
int8_t Encoder_SetFilterWindow(Encoder_t* encoder, uint8_t window_size);
int8_t Encoder_SetFilterParams(Encoder_t* encoder, float noise_var, float process_var);

// 滤波控制接口
int8_t Encoder_EnableFilter(Encoder_t* encoder, uint8_t enable);
int8_t Encoder_ResetFilter(Encoder_t* encoder);
uint8_t Encoder_IsFilterReady(Encoder_t* encoder);

// 数据获取接口（原始和滤波）
int32_t Encoder_GetRawPosition(Encoder_t* encoder);
int32_t Encoder_GetFilteredPosition(Encoder_t* encoder);
float Encoder_GetRawVelocity(Encoder_t* encoder);
float Encoder_GetFilteredVelocity(Encoder_t* encoder);
```

## 7. 集成策略

### 7.1 与现有系统集成
- **文件结构**：遵循现有Components目录结构
- **编译配置**：更新项目包含路径和编译选项
- **应用层封装**：提供与motor_app相似的应用层接口
- **调度集成**：集成到现有的scheduler系统中

### 7.2 硬件资源分配
- **定时器资源**：使用TIM2/TIM3/TIM4作为编码器定时器
- **GPIO资源**：配置相应的GPIO为定时器输入模式
- **中断资源**：可选配置定时器更新中断用于数据处理

### 7.3 兼容性设计
- **多实例支持**：支持多个编码器同时工作
- **参数可配置**：所有关键参数可通过接口配置
- **向后兼容**：不影响现有Motor和HWT101组件

## 8. 测试策略

### 8.1 单元测试
- **函数级测试**：测试每个公共函数的功能正确性
- **边界条件测试**：测试参数边界和异常情况
- **滤波算法测试**：验证滤波算法的有效性

### 8.2 集成测试
- **硬件在环测试**：使用实际编码器进行测试
- **性能测试**：测试CPU占用和内存使用
- **稳定性测试**：长时间运行测试

### 8.3 滤波效果验证
- **噪声抑制测试**：验证滤波器对噪声的抑制效果
- **响应时间测试**：测试滤波器的响应时间
- **精度测试**：验证滤波后数据的精度

## 9. 风险评估与缓解

### 9.1 技术风险
1. **滤波延迟风险**：滤波可能引入延迟
   - 缓解措施：提供可配置的滤波参数，平衡精度和响应时间

2. **计算复杂度风险**：复杂滤波算法可能影响性能
   - 缓解措施：提供多种滤波算法选择，支持关闭滤波

3. **硬件兼容性风险**：不同编码器信号特性差异
   - 缓解措施：提供丰富的硬件配置选项

### 9.2 集成风险
1. **资源冲突风险**：定时器资源可能与其他功能冲突
   - 缓解措施：提供灵活的定时器选择配置

2. **实时性风险**：滤波处理可能影响实时性
   - 缓解措施：优化算法，提供实时性配置选项

## 10. 总结

本技术架构设计完全基于现有Motor和HWT101组件的成功模式，在保持设计一致性的基础上，重点增强了以下核心特性：

1. **滤波系统**：多级滤波架构确保数据稳定性
2. **硬件优化**：充分利用STM32硬件特性
3. **性能优化**：内存和计算效率优化
4. **可配置性**：丰富的配置选项适应不同应用场景
5. **可扩展性**：模块化设计便于功能扩展

该架构设计为后续的编码实现提供了完整的技术指导，确保编码器库的高质量和高可靠性。

---

**文档状态**：已完成  
**下一步行动**：开始详细编码实现